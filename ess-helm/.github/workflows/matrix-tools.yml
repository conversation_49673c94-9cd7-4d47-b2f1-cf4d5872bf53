# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: Build matrix-tool Docker Images

on:
  pull_request_target:
  workflow_dispatch:
  push:
    branches:
    - main
    tags:
    - 'matrix-tools-*'

env:
  REGISTRY_IMAGE: ghcr.io/${{ github.repository }}/matrix-tools
  GO_VERSION: "1.24"

permissions:
  contents: read
  packages: read

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5  # v5
      with:
        go-version: "${{ env.GO_VERSION }}"

    - uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9  # v8
      with:
        working-directory: matrix-tools
        args: -v --show-stats --no-config --modules-download-mode readonly

    - uses: GoTestTools/gotestfmt-action@8b4478c7019be847373babde9300210e7de34bfb  # v2
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Run tests
      run: cd matrix-tools && go test -v  -json ./... 2>&1 | gotestfmt

  # Build our application docker images in parallel.
  docker-build-and-push:
    permissions:
      contents: read
      packages: write
    name: Build matrix-tools container image
    needs:
    - tests
    runs-on: ubuntu-latest
    steps:
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@18ce135bb5112fa8ce4ed6c17ab05699d7f3a5e0  # v3

    - name: Checkout Repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - name: Login to GHCR
      uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772  # v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Get Release Tag
      if: github.ref_type == 'tag' # Only for GitHub releases
      run: |
        echo "Converting $GITHUB_REF to tags"
        echo "VERSION=${GITHUB_REF#refs\/tags\/matrix-tools-}" >> "$GITHUB_ENV"

    - name: Docker meta
      id: meta
      uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804  # v5
      with:
        images: |
          ${{ env.REGISTRY_IMAGE }}
        flavor: |
          latest=false
        tags: |
          type=raw,value=${{ env.VERSION }},enable=${{ github.ref_type == 'tag' }}
          type=sha,prefix=sha-,format=short
        labels: |
          org.opencontainers.image.licenses=AGPL-3.0-only
        annotations: |
          org.opencontainers.image.licenses=AGPL-3.0-only

    - name: Build and Push Image
      uses: docker/bake-action@37816e747588cb137173af99ab33873600c46ea8  # v6
      with:
        push: ${{ github.ref_type == 'tag' }}
        files: |
          docker-bake.hcl
          cwd://${{ steps.meta.outputs.bake-file }}
        targets: matrix-tools
        set: |
          *.output=type=image
          *.platform=linux/amd64,linux/arm64
