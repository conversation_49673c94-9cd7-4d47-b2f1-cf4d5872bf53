# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-rtc-minimal.yaml matrix-rtc-secrets-in-helm.yaml matrix-rtc-additional-in-helm.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  ingress:
    host: mrtc.ess.localhost
  livekitAuth:
    key: CHANGEME-oolahd9xooshohSh5IeQu1natheur1oo
    secret:
      value: CHANGEME-gohseengahch9pheedi5OomeHea6maem
  sfu:
    additional:
      example-value:
        config: |
          example: value
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
