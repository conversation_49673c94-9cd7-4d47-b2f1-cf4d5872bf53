# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

global:
  baseDomain: ess.localhost

# To check that templating works against the ingress
serverName: "{{ $.Values.global.baseDomain }}"

ingress:
  controllerType: ingress-nginx

wellKnownDelegation:
  ingress:
    tlsSecret: "{{ $.Release.Name }}-well-known-web-tls"
  baseDomainRedirect:
   url: "https://redirect.localhost/path"

haproxy:
  podSecurityContext:
    runAsGroup: 0
