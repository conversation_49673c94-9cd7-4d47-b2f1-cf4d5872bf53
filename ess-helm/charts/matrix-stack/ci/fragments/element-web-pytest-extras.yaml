# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

serverName: ess.localhost

elementWeb:
  ingress:
    host: element.{{ $.Values.serverName }}
    tlsSecret: "{{ $.Release.Name }}-element-web-tls"

  replicas: 2

  annotations:
    has-no-service-monitor: "true"

  podSecurityContext:
    runAsGroup: 0

  additional:
    user-config.json: |
      {
        "default_server_config": {
          "m.homeserver": {
            "base_url": "https://synapse.{{ $.Values.serverName }}"
          }
        },
        "some_key": {
          "some_value": "https://test.{{ $.Values.serverName }}"
        }
      }
