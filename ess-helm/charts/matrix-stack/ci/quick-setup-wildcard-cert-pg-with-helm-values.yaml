# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: quick-setup-all-enabled.yaml quick-setup-hostnames.yaml quick-setup-wildcard-cert.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres, wellKnownDelegation don't have any required properties to be set and defaults to enabled
elementWeb:
  ingress:
    host: chat.your.tld
ingress:
  tlsSecret: ess-certificate
matrixAuthenticationService:
  ingress:
    host: account.your.tld
matrixRTC:
  ingress:
    host: mrtc.your.tld
serverName: your.tld
synapse:
  ingress:
    host: matrix.your.tld
