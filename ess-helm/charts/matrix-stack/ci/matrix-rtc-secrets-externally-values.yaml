# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-rtc-minimal.yaml matrix-rtc-secrets-externally.yaml matrix-rtc-additional-secrets-externally.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  ingress:
    host: mrtc.ess.localhost
  livekitAuth:
    key: CHANGEME-oolahd9xooshohSh5IeQu1natheur1oo
    secret:
      secret: '{{ $.Release.Name }}-matrix-rtc-external'
      secretKey: livekitSecret
  sfu:
    additional:
      example-value:
        configSecret: '{{ $.Release.Name }}-mrtc-external'
        configSecretKey: config
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
