# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: element-web-minimal.yaml synapse-minimal.yaml matrix-authentication-service-minimal.yaml init-secrets-minimal.yaml postgres-minimal.yaml well-known-minimal.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres, wellKnownDelegation don't have any required properties to be set and defaults to enabled
elementWeb:
  ingress:
    host: element.ess.localhost
matrixAuthenticationService:
  ingress:
    host: mas.ess.localhost
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  ingress:
    host: synapse.ess.localhost
