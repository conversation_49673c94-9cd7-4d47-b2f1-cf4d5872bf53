# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-authentication-service-minimal.yaml matrix-authentication-service-postgres.yaml matrix-authentication-service-postgres-secrets-externally.yaml matrix-authentication-service-secrets-externally.yaml synapse-minimal.yaml synapse-postgres.yaml synapse-postgres-secrets-externally.yaml synapse-secrets-externally.yaml matrix-authentication-service-syn2mas-dryrun.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  additional:
    password-scheme.yml:
      config: |
        passwords:
          schemes:
            - version: 1
              algorithm: bcrypt
            - version: 2
              algorithm: argon2id
  encryptionSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: encryption
  ingress:
    host: mas.ess.localhost
  postgres:
    database: mas
    host: postgres
    password:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: postgresPassword
    user: mas
  privateKeys:
    ecdsaPrime256v1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaPrime256v1
    ecdsaSecp256k1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp256k1
    ecdsaSecp384r1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp384r1
    rsa:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysRSA
  syn2mas:
    dryRun: true
    enabled: true
  synapseOIDCClientSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseOIDC
  synapseSharedSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseShared
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  appservices:
    - secret: '{{ $.Release.Name }}-synapse-external'
      secretKey: bridge_registration.yaml
  ingress:
    host: synapse.ess.localhost
  macaroon:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: macaroon
  postgres:
    database: synapse
    host: ess-postgres
    password:
      secret: '{{ $.Release.Name }}-synapse-external'
      secretKey: postgresPassword
    user: synapse_user
  registrationSharedSecret:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: registrationSharedSecret
  signingKey:
    secret: '{{ $.Release.Name }}-synapse-external'
    secretKey: signingKey
wellKnownDelegation:
  enabled: false
