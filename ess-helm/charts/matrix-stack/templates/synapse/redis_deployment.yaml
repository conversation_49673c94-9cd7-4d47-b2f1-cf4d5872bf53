{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- if (include "element-io.synapse.enabledWorkers" (dict "root" $)) | fromJson }}
{{- with .redis }}
apiVersion: apps/v1
kind: Deployment
metadata:
{{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.synapse-redis.labels" (dict "root" $ "context" (dict "image" .image "labels" .labels)) | nindent 4 }}
    k8s.element.io/redis-config-hash: "{{ include "element-io.synapse-redis.configmap-data" (dict "root" $) | sha1sum }}"
  name: {{ $.Release.Name }}-synapse-redis
  namespace: {{ $.Release.Namespace }}
spec:
  {{ include "element-io.ess-library.deployments.commonSpec" (dict "root" $ "context" (dict "nameSuffix" "synapse-redis")) | nindent 2 }}
  template:
    metadata:
      labels:
        {{- include "element-io.synapse-redis.labels" (dict "root" $ "context" (dict "image" .image "labels" .labels "withChartVersion" false)) | nindent 8 }}
        k8s.element.io/redis-config-hash: "{{ include "element-io.synapse-redis.configmap-data" (dict "root" $) | sha1sum }}"
{{- with .annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
{{- end }}
    spec:
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" "synapse-redis" "deployment" true)) | nindent 6 }}
      containers:
      - name: redis
        args:
        - "/config/redis.conf"
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "synapse-redis")) | nindent 8 }}
        ports:
        - containerPort: 6379
          name: redis
          protocol: TCP
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          tcpSocket:
            port: redis
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          tcpSocket:
            port: redis
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          exec:
            command:
            - redis-cli
            - ping
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        volumeMounts:
        - mountPath: /config/redis.conf
          name: config
          readOnly: true
          subPath: redis.conf
        - mountPath: /data
          name: data
          readOnly: false
      restartPolicy: Always
      volumes:
      - configMap:
          name: "{{ $.Release.Name }}-synapse-redis"
          defaultMode: 420
        name: config
      - emptyDir:
          medium: Memory
        name: data
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
