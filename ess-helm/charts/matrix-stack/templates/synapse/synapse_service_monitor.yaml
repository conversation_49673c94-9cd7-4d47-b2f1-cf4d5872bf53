{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- if $.Capabilities.APIVersions.Has "monitoring.coreos.com/v1/ServiceMonitor" }}
{{- if .serviceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "element-io.synapse.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse
  namespace: {{ $.Release.Namespace }}
spec:
  endpoints:
  - interval: 30s
    port: synapse-metrics
    relabelings:
    - targetLabel: instance
      action: replace
      replacement: {{ tpl .ingress.host $ }}
  selector:
    matchLabels:
      app.kubernetes.io/part-of: matrix-stack
      app.kubernetes.io/component: matrix-server
      k8s.element.io/synapse-instance: {{ $.Release.Name }}-synapse
{{- end }}
{{- end }}
{{- end -}}
{{- end -}}
