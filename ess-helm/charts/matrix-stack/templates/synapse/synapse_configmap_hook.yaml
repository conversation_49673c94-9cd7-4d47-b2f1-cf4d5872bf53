{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if and .enabled (or .checkConfigHook.enabled
                        (and $.Values.matrixAuthenticationService.enabled
                            $.Values.matrixAuthenticationService.syn2mas.enabled)) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.synapse-check-config.labels" (dict "root" $ "context" .) | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
  name: {{ include "element-io.synapse.configmap-name" (dict "root" $ "context" (dict "isHook" true)) }}
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.synapse.configmap-data" (dict "root" $ "context" (dict "isHook" true)) | nindent 2 }}
{{- end -}}
{{- end -}}
