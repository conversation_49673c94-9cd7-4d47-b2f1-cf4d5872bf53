{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled }}
{{- with .checkConfigHook }}
{{ if and .enabled -}}
{{- include "element-io.ess-library.serviceAccount" (dict "root" $ "context" (dict "componentValues" . "nameSuffix" "synapse-check-config" "extraAnnotations" (dict "helm.sh/hook" "pre-install,pre-upgrade" "helm.sh/hook-weight" "0"))) }}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
