{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with $.Values.postgres -}}
{{- if (include "element-io.postgres.enabled" (dict "root" $)) }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    {{- include "element-io.postgres.labels" (dict "root" $ "context" .) | nindent 4 }}
    k8s.element.io/postgres-secret-hash: "{{ include "element-io.postgres.secret-data" (dict "root" $ "context" .) | sha1sum }}"
    k8s.element.io/postgres-config-hash: "{{ include "element-io.postgres.configmap-data" (dict "root" $ "context" .) | sha1sum }}"
  name: {{ $.Release.Name }}-postgres
  namespace: {{ $.Release.Namespace }}
{{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
spec:
  serviceName: {{ $.Release.Name }}-postgres
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: {{ $.Release.Name }}-postgres
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        {{- include "element-io.postgres.labels" (dict "root" $ "context" (dict "image" .image "labels" .labels "withChartVersion" false)) | nindent 8 }}
        k8s.element.io/postgres-secret-hash: "{{ include "element-io.postgres.secret-data" (dict "root" $ "context" .) | sha1sum }}"
        k8s.element.io/postgres-config-hash: "{{ include "element-io.postgres.configmap-data" (dict "root" $ "context" .) | sha1sum }}"
{{- with .annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
{{- end }}
    spec:
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" "postgres" "deployment" false)) | nindent 6 }}
      containers:
      - name: postgres
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "postgres")) | nindent 8 }}
        args:
        {{ include "element-io.postgres.args" (dict "root" $ "context" .) | nindent 8 }}
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        lifecycle:
          preStop:
            exec:
              command: ["pg_ctl", "stop", "-D", "/var/lib/postgres/data", "-w", "-t", "55", "-m", "fast"]
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        volumeMounts:
{{- with (include "element-io.init-secrets.postgres-generated-secrets" (dict "root" $)) | fromYamlArray }}
  {{- range . -}}
    {{- $secretArg := . | splitList ":" }}
    {{- if (index $secretArg 1) | contains "POSTGRES" }}
        - mountPath: /secrets/{{ index $secretArg 0 }}/{{ index $secretArg 1 }}
          name: "secret-generated"
          subPath: "{{ index $secretArg 1 }}"
          readOnly: true
    {{- end }}
  {{- end }}
{{- end }}
{{- range $secret := include "element-io.postgres.configSecrets" (dict "root" $ "context" .) | fromJsonArray }}
{{- with (tpl $secret $) }}
        - mountPath: /secrets/{{ . }}
          name: "secret-{{ . | sha256sum | trunc 12 }}"
          readOnly: true
{{- end }}
{{- end }}
        - name: config
          mountPath: /docker-entrypoint-initdb.d/init-ess-dbs.sh
          subPath: configure-dbs.sh
        - name: database
          mountPath: /var/lib/postgres/data
        - name: temp
          mountPath: /tmp
        - name: var-run
          mountPath: /var/run
      - name: postgres-ess-updater
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
        command:
        - /bin/sh
        - -c
        - >
          while ! pg_isready -d postgres -U postgres -h localhost; do echo "Postgres not yet ready"; sleep 1; done;
          echo "Postgres now ready, so changing password";
          export PGHOST=localhost;
          /bin/sh /var/run/configure-ess-dbs.sh;
          echo "Done";
          trap : TERM INT; sleep infinity & wait
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "postgres")) | nindent 8 }}
        args:
        {{ include "element-io.postgres.args" (dict "root" $ "context" .) | nindent 8 }}
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        lifecycle:
          preStop:
            exec:
              command: ["pg_ctl", "stop", "-D", "/var/lib/postgres/data", "-w", "-t", "55", "-m", "fast"]
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          exec:
            command: ["psql", "-w", "-U", "postgres", "-d", "postgres", "-c", "SELECT 1"]
        ports:
        - containerPort: 5432
          name: postgres
        volumeMounts:
{{- with (include "element-io.init-secrets.postgres-generated-secrets" (dict "root" $)) | fromYamlArray }}
  {{- range . -}}
    {{- $secretArg := . | splitList ":" }}
    {{- if (index $secretArg 1) | contains "POSTGRES" }}
        - mountPath: /secrets/{{ index $secretArg 0 }}/{{ index $secretArg 1 }}
          name: "secret-generated"
          subPath: "{{ index $secretArg 1 }}"
          readOnly: true
    {{- end }}
  {{- end }}
{{- end }}
{{- range $secret := include "element-io.postgres.configSecrets" (dict "root" $ "context" .) | fromJsonArray }}
{{- with (tpl $secret $) }}
        - mountPath: /secrets/{{ . }}
          name: "secret-{{ . | sha256sum | trunc 12 }}"
          readOnly: true
{{- end }}
{{- end }}
        - name: config
          mountPath: /var/run/configure-ess-dbs.sh
          subPath: configure-dbs.sh
        - name: var-run
          mountPath: /var/run
{{- with .postgresExporter }}
      - name: postgres-exporter
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "postgres-exporter")) | nindent 8 }}
        ports:
        - name: metrics
          containerPort: 9187
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          httpGet:
            path: /metrics
            port: metrics
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          httpGet:
            path: /metrics
            port: metrics
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          httpGet:
            path: /metrics
            port: metrics
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
{{- end }}
      terminationGracePeriodSeconds: 60
      volumes:
      - emptyDir: {}
        name: temp
      - emptyDir: {}
        name: var-run
{{- if and $.Values.initSecrets.enabled (include "element-io.init-secrets.postgres-generated-secrets" (dict "root" $)) }}
      - secret:
          secretName: {{ $.Release.Name }}-generated
        name: secret-generated
{{- end }}
{{- range $secret := include "element-io.postgres.configSecrets" (dict "root" $ "context" .) | fromJsonArray }}
{{- with (tpl $secret $) }}
      - secret:
          secretName: {{ . }}
        name: "secret-{{ . | sha256sum | trunc 12 }}"
{{- end }}
{{- end }}
      - name: config
        configMap:
          name: {{ $.Release.Name }}-postgres
      - name: database
        persistentVolumeClaim:
          claimName: {{ $.Release.Name }}-postgres-data
{{- end }}
{{- end }}
