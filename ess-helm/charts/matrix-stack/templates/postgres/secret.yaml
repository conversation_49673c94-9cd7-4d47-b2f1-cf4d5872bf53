{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.postgres -}}
{{- if (include "element-io.postgres.enabled" (dict "root" $)) }}
{{- if or .adminPassword.value (include "element-io.postgres.anyEssPasswordHasValue" (dict "root" $ "context" .)) }}
apiVersion: v1
kind: Secret
metadata:
  labels:
    {{- include "element-io.postgres.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ include "element-io.postgres.secret-name" (dict "root" $ "context"  (dict "isHook" false)) }}
  namespace: {{ $.Release.Namespace }}
{{- include "element-io.postgres.secret-data" (dict "root" $ "context" .) -}}
{{- end -}}
{{- end -}}
{{- end -}}
