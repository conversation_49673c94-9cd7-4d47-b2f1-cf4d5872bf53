{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.initSecrets -}}
{{- if .enabled -}}
{{- if and .enabled (include "element-io.init-secrets.generated-secrets" (dict "root" $)) -}}
{{- include "element-io.ess-library.serviceAccount" (dict "root" $ "context" (dict "componentValues" . "nameSuffix" "init-secrets" "extraAnnotations" (dict "helm.sh/hook" "pre-install,pre-upgrade" "helm.sh/hook-weight" "-10"))) }}
{{- end }}
{{- end }}
{{- end }}

