{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{ range $step := list "pre" "post" -}}
{{- with $.Values.deploymentMarkers -}}
{{- if .enabled -}}
{{- if and .enabled (include "element-io.deployment-markers.markers" (dict "root" $)) -}}
{{- include "element-io.ess-library.serviceAccount" (dict "root" $ "context" (dict "componentValues" . "nameSuffix" (printf "deployment-markers-%s" $step) "extraAnnotations" (dict "helm.sh/hook" (printf "%s-install,%s-upgrade" $step $step) "helm.sh/hook-weight" "-20"))) }}
---
{{- end }}
{{- end }}
{{- end }}
{{- end }}

