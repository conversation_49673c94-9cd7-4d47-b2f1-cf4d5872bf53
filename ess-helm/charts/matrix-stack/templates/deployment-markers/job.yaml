{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{ range $step := list "pre" "post" -}}
{{- with $.Values.deploymentMarkers -}}
{{- if and .enabled (include "element-io.deployment-markers.markers" (dict "root" $)) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $.Release.Name }}-deployment-markers-{{ $step }}
  namespace: {{ $.Release.Namespace }}
  annotations:
    "helm.sh/hook": {{ $step }}-install,{{ $step }}-upgrade
    "helm.sh/hook-weight": "-20"
{{- with .annotations }}
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.deployment-markers.labels" (dict "root" $ "context" (mustMergeOverwrite (dict "step" $step) .)) | nindent 4 }}
spec:
  backoffLimit: 6
  completionMode: NonIndexed
  completions: 1
  manualSelector: false
  parallelism: 1
  podReplacementPolicy: TerminatingOrFailed
  template:
    metadata:
      annotations:
{{- with .annotations }}
        {{- toYaml . | nindent 8 }}
{{- end }}
      labels:
        {{- include "element-io.deployment-markers.labels" (dict "root" $ "context" (dict "step" $step "labels" .labels "withChartVersion" false)) | nindent 8 }}
    spec:
      restartPolicy: OnFailure
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" (printf "deployment-markers-%s" $step) "deployment" false "usesMatrixTools" true "mountServiceAccountToken" true)) | nindent 6 }}
      containers:
      - name: deployment-markers
{{- with $.Values.matrixTools.image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "deployment-markers")) | nindent 8 }}
        command:
        - "/matrix-tools"
        - "deployment-markers"
        - "-step"
        - {{ $step | quote }}
        - "-markers"
        - {{ include "element-io.deployment-markers.markers" (dict "root" $) | fromYamlArray | join "," | quote }}
        - "-labels"
        - {{ include "element-io.deployment-markers.configmap-labels" (dict "root" $ "context" (dict "labels" .labels "withChartVersion" false)) | trim  | replace ": " "="| replace "\n" "," | replace "\"" "" | quote }}
---
{{- end -}}
{{- end -}}
{{- end -}}
