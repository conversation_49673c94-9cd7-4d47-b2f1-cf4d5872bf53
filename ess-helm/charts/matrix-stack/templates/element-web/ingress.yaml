{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.elementWeb -}}
{{- if .enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
{{- include "element-io.ess-library.ingress.annotations" (dict "root" $ "context" (dict "ingress" .ingress)) | nindent 2 }}
  labels:
    {{- include "element-io.element-web.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-element-web
  namespace: {{ $.Release.Namespace }}
spec:
{{- include "element-io.ess-library.ingress.tls" (dict "root" $ "context" (dict "ingress" .ingress "ingressName" "element-web")) | nindent 2 }}
{{- include "element-io.ess-library.ingress.className" (dict "root" $ "context" .ingress.className) | nindent 2 }}
  rules:
  - host: {{ (tpl .ingress.host $) | quote }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ $.Release.Name }}-element-web
            port:
              number: 80
{{- end }}
{{- end }}
