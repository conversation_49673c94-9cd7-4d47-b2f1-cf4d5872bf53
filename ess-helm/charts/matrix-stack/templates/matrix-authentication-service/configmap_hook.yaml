{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.matrixAuthenticationService -}}
{{- if and .enabled .syn2mas.enabled (not .syn2mas.dryRun) -}}
{{- $masContext := (mustMergeOverwrite ($.Values.matrixAuthenticationService | deepCopy) (dict "isHook" true)) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "element-io.matrix-authentication-service.configmap-name" (dict "root" $ "context" (dict "isHook" true)) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.matrix-authentication-service.labels" (dict "root" $ "context" $masContext) | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
data:
  {{- include "element-io.matrix-authentication-service.configmap-data" (dict "root" $ "context" $masContext) | nindent 2 -}}
{{ end -}}
{{- end -}}
