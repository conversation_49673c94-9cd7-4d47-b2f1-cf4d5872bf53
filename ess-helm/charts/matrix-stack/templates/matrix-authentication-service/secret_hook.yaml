{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.matrixAuthenticationService -}}
{{- if and .enabled $.Values.synapse.enabled
  (or (and $.Values.synapse.checkConfigHook.enabled
        (include "element-io.matrix-authentication-service.synapse-secret-data" (dict "root" $ "context" .))
        )
       (and .syn2mas.enabled (not .syn2mas.dryRun))
  ) -}}
{{- $masContext := (mustMergeOverwrite ($.Values.matrixAuthenticationService | deepCopy) (dict "isHook" true)) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "element-io.matrix-authentication-service.secret-name" (dict "root" $ "context"  (dict "isHook" true)) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.matrix-authentication-service.labels" (dict "root" $ "context" $masContext) | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-5"
type: Opaque
data:
{{- if .syn2mas.enabled }}
{{- include "element-io.matrix-authentication-service.secret-data" (dict "root" $ "context" $masContext) | nindent 2 }}
{{- else }}
{{- include "element-io.matrix-authentication-service.synapse-secret-data" (dict "root" $ "context" $masContext) | nindent 2 }}
{{- end -}}
{{- end -}}
{{- end -}}
