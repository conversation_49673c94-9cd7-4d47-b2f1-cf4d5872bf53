{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $.Release.Name }}-matrix-rtc-authorisation-service
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "element-io.matrix-rtc-authorisation-service.labels" (dict "root" $ "context" .) | nindent 4 }}
type: Opaque
data:
  {{- include "element-io.matrix-rtc-authorisation-service.secret-data" (dict "root" $ "context" .) | nindent 2 -}}
{{- end -}}
{{- end -}}
