{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- if or $.Values.synapse.enabled $.Values.wellKnownDelegation.enabled -}}
{{- with .Values.haproxy -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.haproxy.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-haproxy
  namespace: {{ $.Release.Namespace }}
spec:
  type: ClusterIP
  ports:
  - name: haproxy-metrics
    port: 8405
    targetPort: haproxy-metrics
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-haproxy"
{{- end -}}
{{- end -}}
