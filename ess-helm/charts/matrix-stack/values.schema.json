{"$id": "file://matrix-stack/", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"global": {"type": "object", "additionalProperties": true}, "serverName": {"type": "string"}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "certManager": {"type": "object", "properties": {"clusterIssuer": {"type": "string"}, "issuer": {"type": "string"}}, "additionalProperties": false}, "matrixTools": {"type": "object", "properties": {"image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}}, "additionalProperties": false}, "imagePullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "deploymentMarkers": {"$id": "file://init-secrets", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "rbac": {"type": "object", "properties": {"create": {"type": "boolean"}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}}, "additionalProperties": false}, "initSecrets": {"$id": "file://init-secrets", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "rbac": {"type": "object", "properties": {"create": {"type": "boolean"}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}}, "additionalProperties": false}, "matrixRTC": {"$id": "file://matrix-rtc", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "livekitAuth": {"type": "object", "oneOf": [{"required": ["keysYaml"], "not": {"required": ["key", "secret"]}}, {"required": ["key", "secret"], "not": {"required": ["keysYaml"]}}], "properties": {"keysYaml": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "key": {"type": "string"}, "secret": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "replicas": {"type": "integer"}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "host": {"type": "string"}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "hostAliases": {"type": "array", "items": {"type": "object", "properties": {"ip": {"type": "string"}, "hostnames": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "sfu": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "hostNetwork": {"type": "boolean"}, "additional": {"type": "object", "additionalProperties": {"type": "object", "oneOf": [{"required": ["config"], "not": {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"], "not": {"required": ["config"]}}], "properties": {"properties": {"config": {"type": "string"}, "configSecret": {"type": "string"}, "configSecretKey": {"type": "string"}}}}}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["debug", "info", "warn", "error"]}, "pionLevel": {"type": "string", "enum": ["debug", "info", "warn", "error"]}, "json": {"type": "boolean"}}, "additionalProperties": false}, "exposedServices": {"type": "object", "properties": {"rtcTcp": {"type": "object", "required": ["enabled", "portType", "port"], "properties": {"enabled": {"type": "boolean"}, "portType": {"type": "string"}, "port": {"type": "number"}}, "additionalProperties": false}, "rtcMuxedUdp": {"type": "object", "required": ["enabled", "portType", "port"], "properties": {"enabled": {"type": "boolean"}, "portType": {"type": "string"}, "port": {"type": "number"}}, "additionalProperties": false}, "rtcUdp": {"type": "object", "required": ["enabled", "portType", "portRange"], "properties": {"enabled": {"type": "boolean"}, "portType": {"type": "string"}, "portRange": {"type": "object", "required": ["startPort", "endPort"], "properties": {"startPort": {"type": "number"}, "endPort": {"type": "number"}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "hostAliases": {"type": "array", "items": {"type": "object", "properties": {"ip": {"type": "string"}, "hostnames": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}, "elementWeb": {"$id": "file://element-web", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "additional": {"type": "object", "additionalProperties": {"type": "string"}}, "replicas": {"minimum": 1, "type": "integer"}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "host": {"type": "string"}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "haproxy": {"$id": "file://haproxy", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"replicas": {"minimum": 1, "type": "integer"}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "matrixAuthenticationService": {"$id": "file://matrixAuthenticationService", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean", "description": "Enable the Matrix Authentication Service (MAS) service."}, "additional": {"type": "object", "additionalProperties": {"type": "object", "oneOf": [{"required": ["config"], "not": {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"], "not": {"required": ["config"]}}], "properties": {"properties": {"config": {"type": "string"}, "configSecret": {"type": "string"}, "configSecretKey": {"type": "string"}}}}}, "encryptionSecret": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "synapseSharedSecret": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "synapseOIDCClientSecret": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "postgres": {"type": "object", "required": ["host", "user", "database"], "properties": {"host": {"type": "string"}, "port": {"type": "integer", "minimum": 0, "maximum": 65535}, "user": {"type": "string"}, "database": {"type": "string"}, "sslMode": {"type": "string", "enum": ["disable", "allow", "prefer", "require", "verify-ca", "verify-full"]}, "password": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "privateKeys": {"default": {}, "type": "object", "description": "The private keys used for signing. Only RSA private key is required.", "properties": {"ecdsaPrime256v1": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "ecdsaSecp256k1": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "ecdsaSecp384r1": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "rsa": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "replicas": {"type": "integer"}, "syn2mas": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Enable synapse to Matrix Authentication Service migration."}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "dryRun": {"type": "boolean", "description": "Run the migration job in dry-run mode. Do not actually migrate the data."}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}}, "additionalProperties": false}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "host": {"type": "string"}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}}, "additionalProperties": false}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "hostAliases": {"type": "array", "items": {"type": "object", "properties": {"ip": {"type": "string"}, "hostnames": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "postgres": {"$id": "file://postgres", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "postgresExporter": {"type": "object", "properties": {"image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "adminPassword": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "essPasswords": {"type": "object", "properties": {"synapse": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "matrixAuthenticationService": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "storage": {"type": "object", "properties": {"existingClaim": {"type": "string"}, "size": {"type": "string"}, "storageClass": {"type": "string"}, "resourcePolicy": {"type": "string", "enum": ["keep", "delete"]}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}}, "additionalProperties": false}, "synapse": {"$id": "file://synapse", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "checkConfigHook": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}, "postgres": {"type": "object", "required": ["host", "user", "database"], "properties": {"host": {"type": "string"}, "port": {"type": "integer", "minimum": 0, "maximum": 65535}, "user": {"type": "string"}, "database": {"type": "string"}, "sslMode": {"type": "string", "enum": ["disable", "allow", "prefer", "require", "verify-ca", "verify-full"]}, "password": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "media": {"type": "object", "properties": {"storage": {"type": "object", "properties": {"existingClaim": {"type": "string"}, "size": {"type": "string"}, "storageClass": {"type": "string"}, "resourcePolicy": {"type": "string", "enum": ["keep", "delete"]}}, "additionalProperties": false}, "maxUploadSize": {"type": "string", "pattern": "^[0-9]+[MK]$"}}, "additionalProperties": false}, "macaroon": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "registrationSharedSecret": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "signingKey": {"type": "object", "properties": {"value": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}, "additional": {"type": "object", "additionalProperties": {"type": "object", "oneOf": [{"required": ["config"], "not": {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, {"required": ["config<PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>"], "not": {"required": ["config"]}}], "properties": {"properties": {"config": {"type": "string"}, "configSecret": {"type": "string"}, "configSecretKey": {"type": "string"}}}}}, "appservices": {"type": "array", "items": {"type": "object", "oneOf": [{"required": ["configMap", "config<PERSON>ap<PERSON><PERSON>"], "not": {"required": ["secret", "secret<PERSON>ey"]}}, {"required": ["secret", "secret<PERSON>ey"], "not": {"required": ["configMap", "config<PERSON>ap<PERSON><PERSON>"]}}], "properties": {"configMap": {"type": "string"}, "configMapKey": {"type": "string"}, "secret": {"type": "string"}, "secretKey": {"type": "string"}}, "additionalProperties": false}}, "logging": {"type": "object", "properties": {"rootLevel": {"type": "string", "enum": ["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"]}, "levelOverrides": {"type": "object", "additionalProperties": {"type": "string", "enum": ["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"]}}}, "additionalProperties": false}, "extraArgs": {"type": "array", "items": {"type": "string"}}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "host": {"type": "string"}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}, "additionalPaths": {"type": "array", "items": {"type": "object", "required": ["path", "availability"], "properties": {"path": {"type": "string"}, "availability": {"type": "string", "enum": ["internally_and_externally", "only_externally", "blocked"]}, "service": {"type": "object", "required": ["name", "port"], "properties": {"name": {"type": "string"}, "port": {"type": "object", "oneOf": [{"required": ["name"], "not": {"required": ["number"]}}, {"required": ["number"], "not": {"required": ["name"]}}], "properties": {"name": {"type": "string"}, "number": {"type": "integer"}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}}}, "additionalProperties": false}, "image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "hostAliases": {"type": "array", "items": {"type": "object", "properties": {"ip": {"type": "string"}, "hostnames": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "serviceMonitors": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "workers": {"type": "object", "properties": {"appservice": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "background": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "client-reader": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "encryption": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "event-creator": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "event-persister": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "federation-inbound": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "federation-reader": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "federation-sender": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "initial-synchrotron": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "media-repository": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "presence-writer": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "push-rules": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "pusher": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "receipts-account": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "sliding-sync": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "sso-login": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "synchrotron": {"required": ["replicas"], "properties": {"enabled": {"type": "boolean"}, "replicas": {"type": "integer", "minimum": 1}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "topologySpreadConstraints": {"type": "array", "items": {"required": ["maxSkew", "<PERSON><PERSON><PERSON>"], "properties": {"labelSelector": {"type": "object", "properties": {"matchExpressions": {"type": "array", "items": {"type": "object", "required": ["key", "operator"], "properties": {"key": {"type": "string"}, "operator": {"type": "string", "enum": ["In", "NotIn", "Exists", "DoesNotExist"]}, "values": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "matchLabels": {"type": ["object", "null"], "additionalProperties": {"type": ["string", "null"]}}}, "additionalProperties": false}, "matchLabelKeys": {"type": ["array", "null"], "items": {"type": "string"}}, "maxSkew": {"type": "integer", "minium": 1}, "minDomains": {"type": "integer", "minium": 0}, "nodeAffinityPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "nodeTaintsPolicy": {"type": "string", "enum": ["Honor", "Ignore"]}, "topologyKey": {"type": "string"}, "whenUnsatisfiable": {"type": "string", "enum": ["DoNotSchedule", "ScheduleAnyway"]}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "typing-persister": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "user-dir": {"properties": {"enabled": {"type": "boolean"}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "type": "object", "additionalProperties": false}}, "additionalProperties": false}, "redis": {"type": "object", "properties": {"image": {"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}, "extraEnv": {"type": "array", "items": {"type": "object", "required": ["name", "value"], "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "additionalProperties": false}}, "containersSecurityContext": {"properties": {"allowPrivilegeEscalation": {"type": "boolean"}, "capabilities": {"properties": {"add": {"items": {"type": "string"}, "type": "array"}, "drop": {"items": {"type": "string"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "readOnlyRootFilesystem": {"type": "boolean"}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}}, "type": "object", "additionalProperties": false}, "nodeSelector": {"type": "object", "additionalProperties": {"type": "string"}}, "podSecurityContext": {"properties": {"fsGroup": {"format": "int64", "type": "integer"}, "fsGroupChangePolicy": {"type": "string"}, "runAsGroup": {"format": "int64", "type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"format": "int64", "type": "integer"}, "seLinuxOptions": {"properties": {"level": {"type": "string"}, "role": {"type": "string"}, "type": {"type": "string"}, "user": {"type": "string"}}, "type": "object", "additionalProperties": false}, "seccompProfile": {"properties": {"localhostProfile": {"type": "string"}, "type": {"enum": ["RuntimeDefault", "Unconfined", "Localhost"], "type": "string"}}, "type": "object", "additionalProperties": false}, "supplementalGroups": {"items": {"format": "int64", "type": "integer"}, "type": "array"}}, "type": "object", "additionalProperties": false}, "resources": {"properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$"}, "type": "object"}}, "type": "object", "additionalProperties": false}, "serviceAccount": {"type": "object", "properties": {"create": {"type": "boolean"}, "name": {"type": "string"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "tolerations": {"type": "array", "items": {"properties": {"effect": {"type": "string", "enum": ["NoSchedule", "PreferNoSchedule", "NoExecute"]}, "key": {"type": "string"}, "operator": {"type": "string"}, "tolerationSeconds": {"type": "number"}, "value": {"type": "string"}}, "type": "object", "additionalProperties": false}}, "livenessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "readinessProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}, "startupProbe": {"type": "object", "properties": {"failureThreshold": {"type": ["integer", "null"], "minimum": 1}, "initialDelaySeconds": {"type": ["integer", "null"], "minimum": 0}, "periodSeconds": {"type": ["integer", "null"], "minimum": 1}, "successThreshold": {"type": ["integer", "null"], "minimum": 1}, "timeoutSeconds": {"type": ["integer", "null"], "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}, "wellKnownDelegation": {"$id": "file://wellKnownDelegation", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}, "additionalProperties": false}}, "additionalProperties": false}, "labels": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "baseDomainRedirect": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "url": {"type": "string"}}, "additionalProperties": false}, "additional": {"type": "object", "properties": {"client": {"type": "string"}, "element": {"type": "string"}, "server": {"type": "string"}, "support": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}