{"$id": "file://wellKnownDelegation", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "ingress": {"$ref": "file://common/ingress_without_host.json"}, "labels": {"$ref": "file://common/labels.json"}, "baseDomainRedirect": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "url": {"type": "string"}}}, "additional": {"type": "object", "properties": {"client": {"type": "string"}, "element": {"type": "string"}, "server": {"type": "string"}, "support": {"type": "string"}}}}}