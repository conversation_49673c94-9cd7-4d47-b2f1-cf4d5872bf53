{"$id": "file://init-secrets", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "rbac": {"type": "object", "properties": {"create": {"type": "boolean"}}}, "labels": {"$ref": "file://common/labels.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}}}