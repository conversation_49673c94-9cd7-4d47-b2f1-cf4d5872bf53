{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}

{% macro single_worker(workerType) %}
{{ workerType }}:
  ## Set to true to deploy this worker
  enabled: false

  ## Resources for this worker.
  ## If omitted the global Synapse resources are used
  # resources: {}

{{- sub_schema_values.probe("liveness", failureThreshold=8, periodSeconds=6, timeoutSeconds=2) | indent(2) }}
{{- sub_schema_values.probe("readiness", failureThreshold=8, periodSeconds=2, successThreshold=2, timeoutSeconds=2) | indent(2) }}
{{- sub_schema_values.probe("startup", failureThreshold=54, periodSeconds=2) | indent(2) }}
{%- endmacro %}

{% macro scalable_worker(workerType) %}
{{ workerType }}:
  ## Set to true to deploy this worker
  enabled: false

  ## The number of replicas of this worker to run
  replicas: 1

  ## Resources for this worker.
  ## If omitted the global Synapse resources are used
  # resources: {}

{{- sub_schema_values.probe("liveness", periodSeconds=6, timeoutSeconds=2) | indent(2) }}
{{- sub_schema_values.probe("readiness", periodSeconds=2, successThreshold=2, timeoutSeconds=2) | indent(2) }}
{{- sub_schema_values.probe("startup", failureThreshold=21, periodSeconds=2) | indent(2) }}
{%- endmacro %}
